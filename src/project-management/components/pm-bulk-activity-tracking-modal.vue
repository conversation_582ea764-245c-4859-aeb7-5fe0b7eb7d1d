<script setup>
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import DOMPurify from 'dompurify';
import { keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useMembers } from '~/common/composables/members.js';
import { stringToNumber } from '~/common/utils/common.utils.js';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  activity: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['close']);

dayjs.extend(isSameOrBefore);

const $t = inject('$t');

const { getUserDetails } = useMembers();

const project_management_store = useProjectManagementStore();
const { $g, active_schedule, is_fullscreen } = storeToRefs(project_management_store);

// TODO: API integration
// TODO: Cell paste and copy

const hot$ = ref(null);

const state = reactive({
  interval: 'daily',
  range: 'this_week',
  hot_data: [],
  hot_instance: null,
  activities: [],
  invalid_data: {
    activity_with_resource_but_empty_values: null,
  },
});

const schedule_resources_map = computed(() => {
  return keyBy(active_schedule.value.resources, 'uid');
});

const interval_options = [
  ['daily', 'Daily'],
  ['weekly', 'Weekly'],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
}));

const range_options = [
  ['this_week', 'This week', dayjs().startOf('week'), dayjs().endOf('week')],
  ['this_month', 'This month', dayjs().startOf('month'), dayjs().endOf('month')],
  ['last_7_days', 'Last 7 days', dayjs().subtract(7, 'days'), dayjs()],
  ['last_14_days', 'Last 14 days', dayjs().subtract(14, 'days'), dayjs()],
  ['last_30_days', 'Last 30 days', dayjs().subtract(30, 'days'), dayjs()],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
  min_date: item[2],
  max_date: item[3],
}));

const dates = computed(() => {
  const current_range = range_options.find(option => option.value === state.range);
  let dates = getDatesBetween(current_range.min_date, current_range.max_date);

  if (state.interval === 'weekly') {
    dates = dates.filter(date => dayjs(date).day() === 1);
  }

  const dates_map = {};
  dates.forEach((date) => {
    const key = state.interval === 'weekly' ? `${date.format('YYYY-MM-DD')}_${date.add(6, 'day').format('YYYY-MM-DD')}` : date.format('YYYY-MM-DD');
    const label = state.interval === 'weekly' ? `${dayjs(date).format('DD MMM YYYY')} to ${dayjs(date).add(6, 'day').format('DD MMM YYYY')}` : dayjs(date).format('DD MMMM YYYY');
    dates_map[key] = {
      label,
    };
  });
  return dates_map;
});

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_data.length * 30) + 87; // 71 - header, 1 - first row, 15 - scrollbar
  if (calculated_height > 550)
    calculated_height = 550;
  if (calculated_height < 200)
    calculated_height = 200;
  return `${calculated_height}px`;
});

const hot_columns = computed(() => {
  const dateColumns = Object.keys(dates.value).map(date => ({
    header: dates.value[date].label,
    columns: [
      {
        data: `${date}_work`,
        header: $t('Work'),
        type: 'numeric',
        width: 80,
      },
      {
        data: `${date}_cost`,
        header: $t('Cost'),
        type: 'numeric',
        width: 100,
      },
      {
        data: `${date}_duration`,
        header: $t('Duration'),
        type: 'numeric',
        width: 80,
        readOnly: true,
      },
      {
        data: `${date}_items`,
        header: $t('Items'),
        type: 'numeric',
        width: 80,
        readOnly: true,
      },
    ],
  }));

  return [
    {
      data: 'id',
      header: $t('ID'),
      readOnly: true,
    },
    {
      data: 'activity',
      header: $t('Activity'),
      readOnly: true,
      wordWrap: false,
    },
    {
      data: 'resource',
      validator: 'default-validator',
      type: 'autocomplete',
      header: $t('Resource'),
      width: 230,
      wordWrap: false,
    },
    ...dateColumns.flatMap(dateCol => dateCol.columns),
  ];
});

const hot_nested_headers = computed(() => {
  const firstLevel = [
    $t('ID'),
    $t('Activity'),
    $t('Resource'),
    ...Object.keys(dates.value).map(date => ({ label: dates.value[date].label, colspan: 4 })),
  ];

  const secondLevel = [
    '',
    '',
    '',
    ...Object.keys(dates.value).flatMap(() => [
      $t('Work'),
      $t('Cost'),
      $t('Duration'),
      $t('Items'),
    ]),
  ];

  return [firstLevel, secondLevel];
});

const hierarchy_breadcrumbs = computed(() => {
  const parents = [];
  $g.value.eachParent((task) => {
    parents.push({
      value: task.uid,
      label: task.text,
      uid: task.id,
      has_children: true,
      truncate_length: Math.min(task.text?.length, 20),
    });
  }, props.activity.id);
  return parents.reverse();
});

function getDatesBetween(min_date, max_date) {
  const dates = [];
  let current_date = min_date || dayjs(min_date);
  const end_date = max_date || dayjs(max_date);
  while (current_date.isSameOrBefore(end_date)) {
    dates.push(current_date);
    current_date = current_date.add(1, 'day');
  }
  return dates;
}

function memberResourceRenderer(instance, td, row, col, prop, value, cellProperties) {
  td.innerHTML = '';
  if (cellProperties.valid === false) {
    td.className += ' htInvalid';
  }
  td.className += ' htMiddle';
  const { members_details } = getUserDetails(schedule_resources_map.value[value]?.external_id);
  members_details?.forEach((user) => {
    if (user) {
      const display_name = user.name;

      const user_details = {
        bg_color: stringToNumber(user?.name),
        label: DOMPurify.sanitize(display_name.trim(), { ALLOWED_TAGS: [] }),
        id: user.uid,
        avatar: user?.display_picture ?? '',
        email: DOMPurify.sanitize(user?.email, { ALLOWED_TAGS: [] }),
      };

      const container = document.createElement('div');
      container.style.display = 'flex';
      container.classList = `gap-1`;
      container.style.alignItems = 'center';
      container.style.marginBottom = '4px';
      if (user_details.avatar) {
        const img = document.createElement('img');
        img.src = user?.display_picture ?? '';
        img.className = `rounded-full object-cover !text-xs !w-4 !h-4 flex justify-center`;
        img.style.width = '24px';
        img.style.height = '24px';
        container.appendChild(img);
      }
      else {
        const span = document.createElement('span');
        span.className = `rounded-full !text-xs !w-4 !h-4 flex justify-center`;
        span.style.width = '24px';
        span.style.height = '24px';
        span.style.backgroundColor = user_details?.bg_color;
        span.textContent = user_details.label?.substring(0, 1);
        span.style.color = 'white';
        span.style.alignContent = 'center';
        container.appendChild(span);
      }
      const name = document.createElement('span');
      name.textContent = user_details.label;

      container.appendChild(name);
      td.appendChild(container);
    }
  });
  return td;
}

function customResourceRenderer(instance, td, row, col, _prop, _value, cellProperties) {
  const hot_row_data = instance.getData()[row];
  td.textContent = schedule_resources_map.value[hot_row_data[col]]?.name;
  td.classList.add('htMiddle');

  if (cellProperties.valid === false) {
    td.className += ' htInvalid';
  }

  return td;
}

function afterBeginEditing(row, column) {
  const current_value = state.hot_instance.getDataAtCell(row, column);
  const resource = active_schedule.value.resources.find((resource) => {
    return resource.uid === current_value;
  });
  if (resource?.type === 'member')
    state.hot_instance.getActiveEditor()?.setValue(getUserDetails([resource.external_id]).members_details[0].name);
  else if (resource?.type === 'custom')
    state.hot_instance.getActiveEditor()?.setValue(resource.name);
}

function afterChange(changes, source) {
  logger.log('afterChange', changes, source);

  if (source === 'CopyPaste.paste') {
    changes?.forEach?.((change) => {
      if (change[1] === 'resource') {
        const value = change[3];
        const is_the_value_already_uid = active_schedule.value.resources.find((member) => {
          return member.uid === value || member.uid === value?.[0];
        });

        if (is_the_value_already_uid) {
          // Value is already a UID, do nothing
        }
      }
    });
    state.hot_instance?.render?.();
    return;
  }

  if (source !== 'edit')
    return;

  changes?.forEach?.((change) => {
    if (change[1] === 'resource') {
      const value = change[3];
      const row = change[0];
      const old_value = change[2];

      const member = active_schedule.value.resources.find((resource) => {
        if (resource.type === 'member') {
          const member = getUserDetails([resource.external_id]).members_details[0];
          return member.name === value || member.name === value?.[0];
        }
        else {
          return resource.name === value || resource.name === value?.[0];
        }
      });

      const is_the_value_already_uid = active_schedule.value.resources.find((resource) => {
        return resource.uid === value || resource.uid === value?.[0];
      });

      if (is_the_value_already_uid) {
        // Value is already a UID, do nothing
      }
      else if (member) {
        state.hot_instance.setDataAtCell(change[0], hot_columns.value.findIndex(column => column.data === 'resource'), member.uid);
      }
      else if (!value || value === '' || value === null) {
        if (old_value && old_value !== '') {
          clearDurationAndItemsForRow(row);
        }
      }
    }
  });
  state.hot_instance?.render?.();
}

function clearDurationAndItemsForRow(row) {
  const columnsToUpdate = [];

  Object.keys(dates.value).forEach((date) => {
    const durationColIndex = hot_columns.value.findIndex(column => column.data === `${date}_duration`);
    const itemsColIndex = hot_columns.value.findIndex(column => column.data === `${date}_items`);

    if (durationColIndex !== -1) {
      columnsToUpdate.push([row, durationColIndex, null]);
    }
    if (itemsColIndex !== -1) {
      columnsToUpdate.push([row, itemsColIndex, null]);
    }
  });

  columnsToUpdate.forEach(([rowIndex, colIndex, value]) => {
    state.hot_instance.setDataAtCell(rowIndex, colIndex, value);
  });
}

function beforeKeyDown(event) {
  logger.log('beforeKeyDown', event);
}

function cellsConfiguration(row, col) {
  const cellProperties = {};

  const column_header = hot_columns.value[col]?.data;

  if (!state.hot_instance)
    return cellProperties;

  const row_data = state.hot_instance.getData()[row];
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');

  if (col === resource_col_index) {
    const task = $g.value.getTask(row_data[0]);
    cellProperties.source = task?.resources?.map?.((resource_uid) => {
      const resource = schedule_resources_map.value[resource_uid];
      if (resource.type === 'member')
        return getUserDetails([resource.external_id]).members_details[0].name;
      else if (resource.type === 'custom')
        return resource.name;
      return null;
    });
    if (!cellProperties.source?.length) {
      cellProperties.readOnly = true;
    }
    if (row_data?.[resource_col_index]) {
      const resource = active_schedule.value.resources.find((resource) => {
        return resource.uid === row_data[resource_col_index];
      });

      if (resource?.type === 'member') {
        cellProperties.renderer = memberResourceRenderer;
      }
      else {
        cellProperties.renderer = customResourceRenderer;
      }
    }
  }

  const resource = active_schedule.value.resources.find((resource) => {
    return resource.uid === row_data?.[resource_col_index];
  });
  if (column_header.includes('duration')) {
    if (resource?.cost_type === 'per_hour')
      cellProperties.readOnly = false;
    else
      cellProperties.readOnly = true;
  }
  if (column_header.includes('items')) {
    if (resource?.cost_type === 'per_item')
      cellProperties.readOnly = false;
    else
      cellProperties.readOnly = true;
  }

  return cellProperties;
}

function beforeChange() {
  // For some reason, the members selection is failing if this empty beforeChange function is not called
}

function onHandsOnTableReady(hot_instance) {
  state.hot_instance = hot_instance;

  state.hot_instance.updateSettings({
    cells: cellsConfiguration,
  });

  state.hot_instance.render();
}

function generateInitialData() {
  state.activities = [];
  if ([$g.value.config.types.task, $g.value.config.types.milestone].includes(props.activity.type)) {
    state.activities = [props.activity];
  }
  else {
    state.activities = $g.value.getChildren(props.activity.id)
      .map(child => $g.value.getTask(child))
      .filter((child) => {
        return [$g.value.config.types.task, $g.value.config.types.milestone].includes(child.type);
      });
  }

  return state.activities.map((item) => {
    const baseData = {
      id: item.id,
      activity: item.text,
      resource: item.resource,
    };

    Object.keys(dates.value).forEach((date) => {
      baseData[`${date}_work`] = null;
      baseData[`${date}_cost`] = null;
      baseData[`${date}_duration`] = null;
      baseData[`${date}_items`] = null;
    });

    return baseData;
  });
}

function clearTableData() {
  if (!state.hot_instance) {
    state.hot_data = generateInitialData();
    return;
  }

  const currentData = state.hot_instance.getData();

  const clearedData = currentData.map((row) => {
    const newRow = [...row];

    // Clear all date-related columns (everything after index 2); Keep only the static columns (id, activity, resource - first 3 columns)
    for (let i = 3; i < newRow.length; i++) {
      newRow[i] = null;
    }

    return newRow;
  });

  state.hot_data = state.activities.map((item) => {
    const existingRow = clearedData.find(row => row[0] === item.id);

    const baseData = {
      id: item.id,
      activity: item.text,
      resource: existingRow ? existingRow[2] : item.resource,
    };

    Object.keys(dates.value).forEach((date) => {
      baseData[`${date}_work`] = null;
      baseData[`${date}_cost`] = null;
      baseData[`${date}_duration`] = null;
      baseData[`${date}_items`] = null;
    });

    return baseData;
  });
}

function regenerateTableStructure() {
  if (!state.hot_instance)
    return;

  state.hot_instance.updateSettings({
    columns: hot_columns.value,
    nestedHeaders: hot_nested_headers.value,
    data: state.hot_data,
    cells: cellsConfiguration,
  });

  state.hot_instance.render();
}

function afterGetRowHeader(_row, th) {
  th.classList.add('htMiddle');
}

function beforeCellCopyInterceptor(_data, copiedValue, _dataRowIndex, _dataColIndex, _cellMeta, _row, col) {
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');

  if (col !== resource_col_index) {
    return undefined;
  }

  if (copiedValue) {
    const resource = schedule_resources_map.value[copiedValue];
    if (resource) {
      if (resource.type === 'member') {
        const member_details = getUserDetails([resource.external_id]).members_details[0];
        return member_details?.name || copiedValue;
      }
      else if (resource.type === 'custom') {
        return resource.name;
      }
    }
  }
  return copiedValue;
}

function beforeCellPasteInterceptor(data, pastedValue, dataRowIndex, dataColIndex, cellMeta, cellData, row, col) {
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');
  const column_header = hot_columns.value[col]?.data;

  // Handle resource column
  if (col === resource_col_index) {
    if (pastedValue && typeof pastedValue === 'string') {
      const trimmed_value = pastedValue.trim();

      const resource = active_schedule.value.resources.find((res) => {
        if (res.type === 'member') {
          const member_details = getUserDetails([res.external_id]).members_details[0];
          return member_details?.name === trimmed_value;
        }
        else if (res.type === 'custom') {
          return res.name === trimmed_value;
        }
        return false;
      });

      if (resource) {
        const row_data = state.hot_instance.getData()[row];
        const task = $g.value.getTask(row_data[0]);
        if (task?.resources?.includes(resource.uid)) {
          data[dataRowIndex % data.length][dataColIndex % data[0].length] = resource.uid;
          if (!cellMeta.readOnly) {
            state.hot_instance.setDataAtCell(row, col, resource.uid);
          }
          return;
        }
      }

      data[dataRowIndex % data.length][dataColIndex % data[0].length] = cellData;
    }
    return;
  }

  // Handle duration and items columns - allow pasting even if readOnly due to unvalidated resource
  if (column_header && (column_header.includes('duration') || column_header.includes('items'))) {
    if (pastedValue && !isNaN(pastedValue) && pastedValue.trim() !== '') {
      // Allow pasting numeric values to duration/items columns even if they're readOnly
      data[dataRowIndex % data.length][dataColIndex % data[0].length] = pastedValue;

      // Force update the cell data immediately
      setTimeout(() => {
        if (state.hot_instance) {
          state.hot_instance.setDataAtCell(row, col, pastedValue);
        }
      }, 0);
    }
  }
}

function onSave() {
  state.hot_data.forEach((row) => {
    const cloned_row = { ...row };
    delete cloned_row.id;
    delete cloned_row.activity;
    delete cloned_row.resource;
    if (row.resource && Object.values(cloned_row).every(value => value === null || value === '')) {
      state.invalid_data.activity_with_resource_but_empty_values = row.activity;
    }
  });
  if (state.invalid_data.activity_with_resource_but_empty_values) {
    return;
  }
  logger.log(state.hot_data);
}

watch([() => state.interval, () => state.range], () => {
  clearTableData();
  regenerateTableStructure();
}, { deep: true });

watch(() => state.hot_data, () => {
  state.invalid_data.activity_with_resource_but_empty_values = null;
}, { deep: true });

onMounted(() => {
  state.hot_data = generateInitialData();
});
</script>

<template>
  <HawkModalContainer
    :options="{ teleportTo: is_fullscreen ? '#pm-fullscreen-container' : 'body', escToClose: false }"
    content_class="w-[80vw]"
  >
    <HawkModalHeader @close="emit('close')">
      <template #title>
        <HawkBreadcrumbs
          :items="hierarchy_breadcrumbs"
          :show_active_color="false"
          :max_tags_to_display="3"
        />
        <div>
          <template v-if="state.activities.length > 1">
            {{ $t('Track the subtasks of') }} <span class="text-primary-600">{{ props.activity.text }} ({{ $g.getWBSCode(props.activity) }})</span>
          </template>
          <template v-else>
            {{ $t('Track') }} <span class="text-primary-600">{{ props.activity.text }} ({{ $g.getWBSCode(props.activity) }})</span>
          </template>
        </div>
      </template>
    </HawkModalHeader>
    <HawkModalContent>
      <div class="flex justify-between items-center mb-6">
        <div class="text-sm font-normal text-gray-900">
          {{ $t('Track work done, amount spent for an activity, or record for a particular resource.') }}
        </div>
        <div class="flex items-center gap-2">
          <HawkMenu
            position="fixed"
            :items="range_options"
            @select="state.range = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Range') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ range_options.find((option) => option.value === state.range)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
          <HawkMenu
            position="fixed"
            :items="interval_options"
            @select="state.interval = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Interval') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ interval_options.find((option) => option.value === state.interval)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
        </div>
      </div>
      <HawkHandsOnTable
        v-if="state.hot_data.length"
        ref="hot$"
        :hot-settings="{
          afterChange,
          beforeChange,
          beforeKeyDown,
          afterBeginEditing,
          afterGetRowHeader,
          nestedRows: false,
          autoRowSize: true,
          rowHeights: '30px',
          bindRowsWithHeaders: true,
          nestedHeaders: hot_nested_headers,
          cells: cellsConfiguration,
          fixedColumnsStart: 3,
          className: 'htMiddle',
          contextMenu: false,
          dropdownMenu: false,
          columnSorting: false,
          headerClassName: 'htCenter',
        }"
        :right-click-menu="{}"
        :data="state.hot_data"
        :columns="hot_columns"
        :columns-menu="{ items: {} }"
        :before-cell-paste-interceptor="beforeCellPasteInterceptor"
        :before-cell-copy-interceptor="beforeCellCopyInterceptor"
        class="pm-excel-modal"
        :height="hot_table_height"
        @ready="onHandsOnTableReady"
      />
      <div v-if="state.invalid_data.activity_with_resource_but_empty_values" class="text-sm text-error-600 mt-3">
        {{ $t('The activity') }}
        <span class="font-semibold">{{ state.invalid_data.activity_with_resource_but_empty_values }}</span>
        {{ $t('has a resource selected but no values entered. Please enter at least one value or remove the resource.') }}
      </div>
    </HawkModalContent>
    <HawkModalFooter>
      <template #right>
        <div class="flex justify-end w-full col-span-full">
          <HawkButton
            type="outlined"
            class="mr-4"
            @click="emit('close')"
          >
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            color="primary"
            @click="onSave"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </HawkModalFooter>
  </HawkModalContainer>
</template>

<style lang="scss">
.pm-excel-modal {
  .changeType {
    @apply hidden;
  }

  .handsontableInput {
    line-height: 29px;
  }

  th.afterHiddenColumn::before{
    content: '' !important;
  }

  .htCommentCell {
    @apply bg-[#FFBEBA];
  }
}
</style>
